import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Plus, Edit, Trash2, Eye, Upload, FileText, Video, Download, ExternalLink, Clock, Users } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Resource {
  id: number;
  title: string;
  description: string;
  image_url: string;
  resource_type: string;
  content_url: string;
  external_link: boolean;
  reading_time: number;
  difficulty_level: string;
  tags: string[];
  author: string;
  published_date: string;
  is_featured: boolean;
  is_active: boolean;
  display_order: number;
  view_count: number;
  created_at: string;
  updated_at: string;
}

export default function Resources() {
  const { toast } = useToast();
  const [isAddingResource, setIsAddingResource] = useState(false);
  const [editingResource, setEditingResource] = useState<Resource | null>(null);
  const [selectedType, setSelectedType] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");

  const [resources, setResources] = useState<Resource[]>([
    {
      id: 1,
      title: "Getting Started with React Hooks",
      description: "A comprehensive guide to understanding and implementing React Hooks in modern applications.",
      image_url: "",
      resource_type: "tutorial",
      content_url: "https://example.com/react-hooks-guide",
      external_link: true,
      reading_time: 15,
      difficulty_level: "beginner",
      tags: ["React", "JavaScript", "Frontend"],
      author: "John Smith",
      published_date: "2024-01-15",
      is_featured: true,
      is_active: true,
      display_order: 1,
      view_count: 1250,
      created_at: "2024-01-15T10:00:00Z",
      updated_at: "2024-01-15T10:00:00Z"
    },
    {
      id: 2,
      title: "API Design Best Practices",
      description: "Learn how to design robust and scalable APIs with proper documentation and versioning.",
      image_url: "",
      resource_type: "guide",
      content_url: "/docs/api-design.pdf",
      external_link: false,
      reading_time: 25,
      difficulty_level: "intermediate",
      tags: ["API", "Backend", "Best Practices"],
      author: "Sarah Johnson",
      published_date: "2024-01-10",
      is_featured: false,
      is_active: true,
      display_order: 2,
      view_count: 890,
      created_at: "2024-01-10T10:00:00Z",
      updated_at: "2024-01-10T10:00:00Z"
    },
    {
      id: 3,
      title: "Database Optimization Techniques",
      description: "Advanced strategies for optimizing database performance and query efficiency.",
      image_url: "",
      resource_type: "video",
      content_url: "https://youtube.com/watch?v=example",
      external_link: true,
      reading_time: 45,
      difficulty_level: "advanced",
      tags: ["Database", "Performance", "SQL"],
      author: "Mike Chen",
      published_date: "2024-01-05",
      is_featured: true,
      is_active: true,
      display_order: 3,
      view_count: 2100,
      created_at: "2024-01-05T10:00:00Z",
      updated_at: "2024-01-05T10:00:00Z"
    }
  ]);

  const resourceTypes = ["tutorial", "guide", "case_study", "whitepaper", "video", "blog_post"];
  const difficultyLevels = ["beginner", "intermediate", "advanced"];

  const filteredResources = resources.filter(resource => {
    const matchesType = selectedType === "all" || resource.resource_type === selectedType;
    const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesType && matchesSearch;
  });

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case "beginner": return "text-green-600 bg-green-50";
      case "intermediate": return "text-yellow-600 bg-yellow-50";
      case "advanced": return "text-red-600 bg-red-50";
      default: return "text-gray-600 bg-gray-50";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "video": return <Video className="w-4 h-4" />;
      case "tutorial": return <FileText className="w-4 h-4" />;
      case "guide": return <FileText className="w-4 h-4" />;
      case "case_study": return <FileText className="w-4 h-4" />;
      case "whitepaper": return <FileText className="w-4 h-4" />;
      case "blog_post": return <FileText className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const handleSaveResource = (resource: Partial<Resource>) => {
    if (editingResource) {
      const updatedResource = {
        ...resource,
        updated_at: new Date().toISOString()
      };
      setResources(prev => prev.map(r => r.id === editingResource.id ? { ...r, ...updatedResource } : r));
      toast({ title: "Resource Updated", description: "Resource has been successfully updated." });
    } else {
      const newResource: Resource = {
        id: Date.now(),
        title: resource.title || "",
        description: resource.description || "",
        image_url: resource.image_url || "",
        resource_type: resource.resource_type || "tutorial",
        content_url: resource.content_url || "",
        external_link: resource.external_link || false,
        reading_time: resource.reading_time || 5,
        difficulty_level: resource.difficulty_level || "beginner",
        tags: resource.tags || [],
        author: resource.author || "",
        published_date: resource.published_date || new Date().toISOString().split('T')[0],
        is_featured: resource.is_featured || false,
        is_active: resource.is_active ?? true,
        display_order: resource.display_order || 0,
        view_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      setResources(prev => [...prev, newResource]);
      toast({ title: "Resource Added", description: "New resource has been successfully added." });
    }
    setIsAddingResource(false);
    setEditingResource(null);
  };

  const deleteResource = (id: number) => {
    setResources(prev => prev.filter(r => r.id !== id));
    toast({ title: "Resource Deleted", description: "Resource has been removed." });
  };

  const ResourceForm = ({ resource }: { resource?: Resource }) => {
    const [formData, setFormData] = useState({
      title: resource?.title || "",
      description: resource?.description || "",
      image_url: resource?.image_url || "",
      resource_type: resource?.resource_type || "tutorial",
      content_url: resource?.content_url || "",
      external_link: resource?.external_link || false,
      reading_time: resource?.reading_time || 5,
      difficulty_level: resource?.difficulty_level || "beginner",
      tags: resource?.tags?.join(", ") || "",
      author: resource?.author || "",
      published_date: resource?.published_date || new Date().toISOString().split('T')[0],
      is_featured: resource?.is_featured || false,
      is_active: resource?.is_active ?? true,
      display_order: resource?.display_order || 0,
    });

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Resource title"
            />
          </div>
          <div>
            <Label htmlFor="author">Author</Label>
            <Input
              id="author"
              value={formData.author}
              onChange={(e) => setFormData(prev => ({ ...prev, author: e.target.value }))}
              placeholder="Author name"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Brief description of the resource"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-3 gap-4">
          <div>
            <Label htmlFor="resource_type">Type</Label>
            <Select value={formData.resource_type} onValueChange={(value) => setFormData(prev => ({ ...prev, resource_type: value }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {resourceTypes.map(type => (
                  <SelectItem key={type} value={type}>{type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ')}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="difficulty_level">Difficulty</Label>
            <Select value={formData.difficulty_level} onValueChange={(value) => setFormData(prev => ({ ...prev, difficulty_level: value }))}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {difficultyLevels.map(level => (
                  <SelectItem key={level} value={level}>{level.charAt(0).toUpperCase() + level.slice(1)}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="reading_time">Reading Time (minutes)</Label>
            <Input
              id="reading_time"
              type="number"
              min="1"
              value={formData.reading_time}
              onChange={(e) => setFormData(prev => ({ ...prev, reading_time: parseInt(e.target.value) || 5 }))}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="content_url">Content URL</Label>
          <Input
            id="content_url"
            value={formData.content_url}
            onChange={(e) => setFormData(prev => ({ ...prev, content_url: e.target.value }))}
            placeholder="https://example.com or /docs/file.pdf"
          />
          <div className="flex items-center space-x-2 mt-2">
            <Switch
              id="external_link"
              checked={formData.external_link}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, external_link: checked }))}
            />
            <Label htmlFor="external_link">External Link</Label>
          </div>
        </div>

        <div>
          <Label htmlFor="tags">Tags (comma-separated)</Label>
          <Input
            id="tags"
            value={formData.tags}
            onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
            placeholder="React, JavaScript, Frontend"
          />
        </div>

        <div>
          <Label htmlFor="image_url">Image URL</Label>
          <Input
            id="image_url"
            value={formData.image_url}
            onChange={(e) => setFormData(prev => ({ ...prev, image_url: e.target.value }))}
            placeholder="https://example.com/image.jpg"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="published_date">Published Date</Label>
            <Input
              id="published_date"
              type="date"
              value={formData.published_date}
              onChange={(e) => setFormData(prev => ({ ...prev, published_date: e.target.value }))}
            />
          </div>
          <div>
            <Label htmlFor="display_order">Display Order</Label>
            <Input
              id="display_order"
              type="number"
              value={formData.display_order}
              onChange={(e) => setFormData(prev => ({ ...prev, display_order: parseInt(e.target.value) || 0 }))}
              placeholder="0"
            />
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Switch
              id="is_featured"
              checked={formData.is_featured}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_featured: checked }))}
            />
            <Label htmlFor="is_featured">Featured Resource</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
            />
            <Label htmlFor="is_active">Published</Label>
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={() => { setIsAddingResource(false); setEditingResource(null); }}>
            Cancel
          </Button>
          <Button onClick={() => handleSaveResource({ 
            ...formData, 
            tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag) 
          })}>
            {resource ? "Update" : "Add"} Resource
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Resources Management</h1>
        <Button onClick={() => setIsAddingResource(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Add Resource
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-64">
              <Input
                placeholder="Search resources..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="min-w-48">
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {resourceTypes.map(type => (
                    <SelectItem key={type} value={type}>{type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ')}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Badge variant="secondary">
              {filteredResources.length} resources
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Resources Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredResources.map((resource) => (
          <Card key={resource.id} className={`relative ${!resource.is_active ? "opacity-60" : ""}`}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-2">
                  {getTypeIcon(resource.resource_type)}
                  <Badge variant="outline">{resource.resource_type.charAt(0).toUpperCase() + resource.resource_type.slice(1).replace('_', ' ')}</Badge>
                  {resource.is_featured && (
                    <Badge className="bg-yellow-100 text-yellow-800">Featured</Badge>
                  )}
                </div>
                <div className="flex space-x-1">
                  {resource.external_link && (
                    <Button size="sm" variant="outline" asChild>
                      <a href={resource.content_url} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    </Button>
                  )}
                  <Button size="sm" variant="outline" onClick={() => setEditingResource(resource)}>
                    <Edit className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => deleteResource(resource.id)}>
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <h3 className="font-semibold text-lg mb-2">{resource.title}</h3>
              <p className="text-muted-foreground text-sm mb-4 line-clamp-3">{resource.description}</p>

              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span>Author:</span>
                  <span className="font-medium">{resource.author}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Difficulty:</span>
                  <Badge className={getDifficultyColor(resource.difficulty_level)}>
                    {resource.difficulty_level.charAt(0).toUpperCase() + resource.difficulty_level.slice(1)}
                  </Badge>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{resource.reading_time} min</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-1">
                      <Eye className="w-4 h-4" />
                      <span>{resource.view_count}</span>
                    </div>
                  </div>
                </div>

                <div className="pt-3 border-t">
                  <div className="flex flex-wrap gap-1">
                    {resource.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="text-xs text-muted-foreground pt-2">
                  Published: {new Date(resource.published_date).toLocaleDateString()}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Add Resource Dialog */}
      <Dialog open={isAddingResource} onOpenChange={setIsAddingResource}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Resource</DialogTitle>
          </DialogHeader>
          <ResourceForm />
        </DialogContent>
      </Dialog>

      {/* Edit Resource Dialog */}
      <Dialog open={!!editingResource} onOpenChange={() => setEditingResource(null)}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Resource</DialogTitle>
          </DialogHeader>
          {editingResource && <ResourceForm resource={editingResource} />}
        </DialogContent>
      </Dialog>
    </div>
  );
}