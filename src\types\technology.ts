// TypeScript interfaces matching the backend technology models

export interface TechnologyCategory {
  id: number;
  name: string;
  display_name: string;
  description: string | null;
  is_active: boolean;
  display_order: number;
  created_at: string;
  // Computed properties from backend
  technology_count?: number;
}

export interface Technology {
  id: number;
  name: string;
  description: string | null;
  category_id: number;
  is_featured: boolean;
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
  // Populated from relationship
  category?: TechnologyCategory;
}

// Type for creating a new technology category (without auto-generated fields)
export interface CreateTechnologyCategory {
  name: string;
  display_name: string;
  description?: string | null;
  is_active?: boolean;
  display_order?: number;
}

// Type for updating a technology category (all fields optional except id)
export interface UpdateTechnologyCategory {
  id: number;
  name?: string;
  display_name?: string;
  description?: string | null;
  is_active?: boolean;
  display_order?: number;
}

// Type for creating a new technology (without auto-generated fields)
export interface CreateTechnology {
  name: string;
  description?: string | null;
  category_id: number;
  is_featured?: boolean;
  is_active?: boolean;
  display_order?: number;
}

// Type for updating a technology (all fields optional except id)
export interface UpdateTechnology {
  id: number;
  name?: string;
  description?: string | null;
  category_id?: number;
  is_featured?: boolean;
  is_active?: boolean;
  display_order?: number;
}

// TODO: Add proficiency levels when backend model is clarified

// Helper function to get category display name
export const getCategoryDisplayName = (category: TechnologyCategory): string => {
  return category.display_name || category.name;
};

// Helper function to get featured technologies from a category
export const getFeaturedTechnologies = (technologies: Technology[]): Technology[] => {
  return technologies
    .filter(tech => tech.is_featured && tech.is_active)
    .sort((a, b) => a.display_order - b.display_order);
};

// Helper function to get technologies by category
export const getTechnologiesByCategory = (
  technologies: Technology[], 
  categoryId: number
): Technology[] => {
  return technologies
    .filter(tech => tech.category_id === categoryId)
    .sort((a, b) => a.display_order - b.display_order);
};

// Helper function to get active categories
export const getActiveCategories = (categories: TechnologyCategory[]): TechnologyCategory[] => {
  return categories
    .filter(cat => cat.is_active)
    .sort((a, b) => a.display_order - b.display_order);
};
